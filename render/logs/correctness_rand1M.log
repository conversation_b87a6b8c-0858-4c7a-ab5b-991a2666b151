Rendering to 1024x1024 image
Loaded scene with 1000000 circles
Loaded scene with 1000000 circles
---------------------------------------------------------
Initializing CUDA for <PERSON><PERSON><PERSON><PERSON><PERSON>
Found 8 CUDA devices
Device 0: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 1: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 2: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 3: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 4: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 5: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 6: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 7: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
---------------------------------------------------------

Running benchmark, 1 frames, beginning at frame 0 ...
Dumping frames to logs/output_xxx.ppm
gridDim = 3907, blockDim = 256
Copying image data from device
Wrote image file logs/output_0000.ppm
Copying image data from device
Mismatch detected at pixel [335][298], value = 0.438554, expected 0.545093 for color Red
Mismatch detected at pixel [335][299], value = 0.389541, expected 0.520587 for color Red
Mismatch detected at pixel [335][300], value = 0.383195, expected 0.517414 for color Red
Mismatch detected at pixel [335][301], value = 0.383186, expected 0.517409 for color Red
Mismatch detected at pixel [371][942], value = 0.592650, expected 0.434603 for color Red
Mismatch detected at pixel [371][942], value = 0.859826, expected 0.753211 for color Green
ERROR : Mismatch detected between reference and actual
