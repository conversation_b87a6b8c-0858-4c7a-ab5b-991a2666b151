Rendering to 1024x1024 image
Loaded scene with 1217 circles
Loaded scene with 1217 circles
---------------------------------------------------------
Initializing CUDA for <PERSON><PERSON><PERSON><PERSON><PERSON>
Found 8 CUDA devices
Device 0: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 1: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 2: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 3: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 4: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 5: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 6: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 7: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
---------------------------------------------------------

Running benchmark, 1 frames, beginning at frame 0 ...
Dumping frames to logs/output_xxx.ppm
gridDim = 5, blockDim = 256
Copying image data from device
Wrote image file logs/output_0000.ppm
Copying image data from device
Mismatch detected at pixel [288][0], value = 0.500000, expected 0.750000 for color Green
Mismatch detected at pixel [288][0], value = 0.500000, expected 0.250000 for color Blue
Mismatch detected at pixel [288][1], value = 0.500000, expected 0.750000 for color Green
Mismatch detected at pixel [288][1], value = 0.500000, expected 0.250000 for color Blue
Mismatch detected at pixel [288][2], value = 0.500000, expected 0.750000 for color Green
Mismatch detected at pixel [288][2], value = 0.500000, expected 0.250000 for color Blue
ERROR : Mismatch detected between reference and actual
