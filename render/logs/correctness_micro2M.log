Rendering to 1024x1024 image
Loaded scene with 2000000 circles
Loaded scene with 2000000 circles
---------------------------------------------------------
Initializing CUDA for <PERSON><PERSON><PERSON><PERSON><PERSON>
Found 8 CUDA devices
Device 0: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 1: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 2: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 3: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 4: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 5: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 6: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 7: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
---------------------------------------------------------

Running benchmark, 1 frames, beginning at frame 0 ...
Dumping frames to logs/output_xxx.ppm
gridDim = 7813, blockDim = 256
Copying image data from device
Wrote image file logs/output_0000.ppm
Copying image data from device
Mismatch detected at pixel [14][272], value = 0.983403, expected 0.861179 for color Red
Mismatch detected at pixel [19][270], value = 1.039950, expected 0.897636 for color Red
Mismatch detected at pixel [30][137], value = 0.744360, expected 0.576794 for color Red
Mismatch detected at pixel [72][597], value = 1.064625, expected 0.800675 for color Red
Mismatch detected at pixel [72][597], value = 0.454211, expected 0.327923 for color Blue
Mismatch detected at pixel [73][495], value = 0.699248, expected 0.546394 for color Green
ERROR : Mismatch detected between reference and actual
